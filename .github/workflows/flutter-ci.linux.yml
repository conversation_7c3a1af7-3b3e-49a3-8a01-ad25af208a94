name: Flutter CI - Linux

on:
  workflow_dispatch: # This is a trigger for manual workflow run
  push:
    tags:
      - "v*.*.*"

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v4

    - name: Initialize acore submodule
      run: |
        git submodule update --init lib/corePackages/acore

    # Install dependencies
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        channel: 'stable'

    - name: Install Flutter dependencies
      run: flutter pub get

    - name: Install RPS globally
      run: dart pub global activate rps

    - name: Install dependencies
      run: |
        sudo apt-get update && sudo apt-get upgrade
        sudo apt-get install -y cmake ninja-build build-essential clang pkg-config libgtk-3-dev liblzma-dev
        sudo apt-get install -y libunwind-dev
        sudo apt-get install -y libgstreamer1.0-dev libgstreamer-plugins-base1.0-dev
        sudo apt-get install -y libnotify-dev
        sudo apt-get install -y libayatana-appindicator3-dev
      env:
        CXX: clang++

    # Build for Linux
    - name: Build for Linux
      run: rps release:linux
    
    - name: Verify and Display Build Output
      run: |
        BUILD_PATH="build/linux/x64/release/bundle"
        echo "=== Linux Build Output Verification ==="
        
        if [ -d "$BUILD_PATH" ]; then
          echo "✅ Build directory exists: $BUILD_PATH"
          
          # Get directory size
          DIR_SIZE=$(du -sb "$BUILD_PATH" | cut -f1)
          DIR_SIZE_MB=$(awk "BEGIN {printf \"%.2f\", $DIR_SIZE / 1024 / 1024}")
          echo "📦 Build size: $DIR_SIZE bytes ($DIR_SIZE_MB MB)"
          
          # List main executable and key files
          echo "📁 Build contents:"
          find "$BUILD_PATH" -type f -name "whph" -o -name "*.so" -o -name "*.desktop" | while read file; do
            FILE_SIZE=$(stat -c%s "$file" 2>/dev/null || echo "0")
            FILE_SIZE_KB=$(awk "BEGIN {printf \"%.1f\", $FILE_SIZE / 1024}")
            echo "  - $(basename "$file"): $FILE_SIZE_KB KB"
          done
          
          # Calculate hash of the main executable
          EXECUTABLE="$BUILD_PATH/whph"
          if [ -f "$EXECUTABLE" ]; then
            SHA256=$(sha256sum "$EXECUTABLE" | cut -d' ' -f1)
            echo "🔐 Executable SHA256: $SHA256"
          else
            echo "⚠️  Main executable not found at expected location"
          fi
          
          # Get modification time
          MODIFIED=$(stat -c%y "$BUILD_PATH" 2>/dev/null || echo "Unknown")
          echo "⏰ Modified: $MODIFIED"
          
          echo "=== Build Output Summary ==="
          echo "Path: $BUILD_PATH"
          echo "Size: $DIR_SIZE_MB MB"
          echo "Files: $(find "$BUILD_PATH" -type f | wc -l)"
        else
          echo "❌ ERROR: Build directory not found at $BUILD_PATH"
          echo "Available build directories:"
          find build -type d -name "*release*" 2>/dev/null || echo "No release directories found"
          exit 1
        fi
    
    # Publish the build artifacts
    - name: Get application version
      id: app_version
      run: bash scripts/get_app_version.sh

    - name: Upload Linux build artifact
      uses: actions/upload-artifact@v4
      with:
        name: whph-v${{ env.APP_VERSION }}-linux
        path: build/linux/x64/release/bundle/
        if-no-files-found: error
        compression-level: 6
        overwrite: false
        include-hidden-files: false
