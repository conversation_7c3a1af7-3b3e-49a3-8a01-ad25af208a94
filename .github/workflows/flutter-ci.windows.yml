name: Flutter CI - Windows

on:
  workflow_dispatch: # This is a trigger for manual workflow run
  push:
    tags:
      - "v*.*.*"

jobs:
  build:
    runs-on: windows-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Initialize acore submodule
        run: |
          git submodule update --init lib/corePackages/acore

      # Install dependencies
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          cache: true

      - name: Setup Visual Studio tools
        uses: microsoft/setup-msbuild@v2

      - name: Install Inno Setup
        run: |
          Write-Host "Installing Inno Setup..."
          $url = "https://jrsoftware.org/download.php/is.exe"
          $output = "$env:TEMP\innosetup.exe"
          try {
            Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
            Start-Process -FilePath $output -ArgumentList "/VERYSILENT", "/NORESTART" -Wait
            echo "C:\Program Files (x86)\Inno Setup 6" >> $env:GITHUB_PATH
            Write-Host "Inno Setup installed successfully"
          } catch {
            Write-Host "Failed to install Inno Setup: $($_.Exception.Message)"
            exit 1
          }

      - name: Install Flutter dependencies
        run: |
          flutter config --enable-windows-desktop
          flutter pub get
          Write-Host "Checking for plugin compatibility..."

      - name: Install RPS globally
        run: |
          dart pub global activate rps
          flutter pub deps

      - name: Clean previous builds
        run: flutter clean

      # Add workaround for flutter_local_notifications_windows AOT issue
      - name: Apply Windows build workarounds
        run: |
          Write-Host "Applying workarounds for Windows build issues..."
          
          # Check if the problematic plugin exists
          Write-Host "Checking for flutter_local_notifications_windows plugin..."
          if (Test-Path "pubspec.lock") {
            $pubspecLock = Get-Content "pubspec.lock" -Raw
            if ($pubspecLock -match "flutter_local_notifications_windows") {
              Write-Host "Found flutter_local_notifications_windows plugin - applying AOT workarounds"
              
              # Set environment variables to help with AOT compilation
              echo "FLUTTER_AOT_WORKAROUND=true" >> $env:GITHUB_ENV
              echo "FLUTTER_DISABLE_TREE_SHAKE=true" >> $env:GITHUB_ENV
              echo "FLUTTER_LOCAL_NOTIFICATIONS_DETECTED=true" >> $env:GITHUB_ENV
              
              Write-Host "Plugin version information:"
              Select-String -Path "pubspec.lock" -Pattern "flutter_local_notifications" -Context 5
            } else {
              Write-Host "flutter_local_notifications_windows plugin not found"
            }
          } else {
            Write-Host "pubspec.lock not found"
          }
          
          Write-Host "Setting additional build environment variables..."
          # These flags may help with the AOT compilation issue
          echo "FLUTTER_BUILD_MODE=release" >> $env:GITHUB_ENV

      - name: Build for Windows (with verbose output and multiple strategies)
        run: |
          Write-Host "=== Starting Windows Build Process ==="
          flutter config --enable-windows-desktop
          
          Write-Host "=== Flutter Environment Check ==="
          flutter doctor -v
          
          Write-Host "=== Cleaning Previous Builds ==="
          flutter clean
          
          Write-Host "=== Installing Dependencies ==="
          flutter pub get
          
          Write-Host "=== Attempting Windows Build (Multiple Strategies) ==="
          
          # Strategy 1: Standard release build
          Write-Host "📦 Strategy 1: Standard release build"
          flutter build windows --release --verbose
          if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Standard release build succeeded!"
            echo "BUILD_TYPE=Release" >> $env:GITHUB_ENV
          } else {
            Write-Host "❌ Standard release build failed with exit code: $LASTEXITCODE"
            
            # Strategy 2: Release build with tree-shake disabled
            Write-Host "📦 Strategy 2: Release build with --no-tree-shake-icons"
            flutter build windows --release --no-tree-shake-icons --verbose
            if ($LASTEXITCODE -eq 0) {
              Write-Host "✅ Release build with --no-tree-shake-icons succeeded!"
              echo "BUILD_TYPE=Release" >> $env:GITHUB_ENV
            } else {
              Write-Host "❌ Release build with --no-tree-shake-icons failed with exit code: $LASTEXITCODE"
              
              # Strategy 3: Profile build as fallback
              Write-Host "📦 Strategy 3: Profile build as fallback"
              flutter build windows --profile --verbose
              if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Profile build succeeded as fallback!"
                echo "BUILD_TYPE=Profile" >> $env:GITHUB_ENV
              } else {
                Write-Host "❌ ALL BUILD ATTEMPTS FAILED! Profile build exit code: $LASTEXITCODE"
                exit 1
              }
            }
          }
          
          # Verify BUILD_TYPE was set
          if (-not $env:BUILD_TYPE) {
            Write-Host "⚠️  WARNING: BUILD_TYPE not set, defaulting to Release"
            echo "BUILD_TYPE=Release" >> $env:GITHUB_ENV
          }
          
          Write-Host "🎯 Final BUILD_TYPE: $env:BUILD_TYPE"
          Write-Host "=== Build Process Complete ==="
        env:
          FLUTTER_BUILD_DIR: build

      - name: Debug build output on failure
        if: failure()
        run: |
          Write-Host "Build failed. Checking for build logs..."
          if (Test-Path "build/") {
            Get-ChildItem -Path "build/" -Recurse
          } else {
            Write-Host "No build directory found"
          }
          if (Test-Path "build/windows/") {
            Get-ChildItem -Path "build/windows/" -Recurse
          } else {
            Write-Host "No windows build directory found"
          }
          Write-Host "Checking for log files..."
          $logFiles = Get-ChildItem -Path "build/" -Recurse -Filter "*.log" -ErrorAction SilentlyContinue
          if ($logFiles) {
            foreach ($file in $logFiles) {
              Write-Host "=== Contents of $($file.FullName) ==="
              Get-Content $file.FullName
            }
          } else {
            Write-Host "No log files found"
          }

      # Publish the build artifacts
      - name: Debug build directory structure
        run: |
          Write-Host "=== Build Environment Info ==="
          Write-Host "BUILD_TYPE: Release"
          Write-Host "Current directory: $(Get-Location)"
          Write-Host "=== Build Directory Structure ==="
          if (Test-Path "build") {
            Write-Host "build/ directory contents:"
            Get-ChildItem "build" -Recurse | ForEach-Object {
              if ($_.PSIsContainer) {
                Write-Host "  DIR:  $($_.FullName.Replace((Get-Location).Path, ''))"
              } else {
                Write-Host "  FILE: $($_.FullName.Replace((Get-Location).Path, '')) ($($_.Length) bytes)"
              }
            }
          } else {
            Write-Host "❌ build/ directory does not exist"
          }
          Write-Host "=== End Build Directory Structure ==="

      - name: Verify build output
        run: |
          Write-Host "Verifying build output in Release folder..."
          $buildPath = "build\windows\x64\runner\Release"
          if (Test-Path $buildPath) {
            Write-Host "Build folder exists: $buildPath"
            Get-ChildItem -Path $buildPath -Recurse | Where-Object { -not $_.PSIsContainer } | ForEach-Object {
              Write-Host "File: $($_.FullName) (Size: $($_.Length) bytes)"
            }
          } else {
            Write-Host "ERROR: Build folder not found: $buildPath"
            exit 1
          }

      - name: Create Windows Installer
        run: |
          Write-Host "Creating Windows installer with Inno Setup..."
          
          # Get app version for the installer
          $content = Get-Content 'pubspec.yaml'
          $version = $content | Select-String 'version: (.*)' | ForEach-Object { $_.Matches.Groups[1].Value.Split('+')[0] }
          Write-Host "App version: $version"
          
          # Verify the build output exists
          $buildPath = "build\windows\x64\runner\Release"
          Write-Host "Checking build output at: $buildPath"
          if (-not (Test-Path $buildPath)) {
            Write-Host "ERROR: Build output not found at $buildPath"
            Write-Host "Available build directories:"
            if (Test-Path "build\windows\x64\runner") {
              Get-ChildItem "build\windows\x64\runner" | ForEach-Object { Write-Host "  - $($_.Name)" }
            }
            exit 1
          }
          
          # Create installer directory in the correct location
          $installerDir = "build\windows\installer"
          New-Item -ItemType Directory -Force -Path $installerDir
          Write-Host "Created installer directory: $installerDir"
          
          # Get absolute paths for debugging
          $currentDir = Get-Location
          $absoluteInstallerDir = Join-Path $currentDir $installerDir
          Write-Host "Working directory: $currentDir"
          Write-Host "Absolute installer directory: $absoluteInstallerDir"
          
          # Create a copy of the installer script with dynamic paths
          $issTemplate = Get-Content 'windows\setup-wizard\installer.iss' -Raw
          $issTemplate = $issTemplate -replace 'AppVersion=.*', "AppVersion=$version"
          $issTemplate = $issTemplate -replace 'Source: "\.\.\\build\\windows\\x64\\runner\\Release\\\*"', "Source: `"..\build\windows\x64\runner\Release\*`""
          # Use absolute path for output directory to avoid confusion
          $issTemplate = $issTemplate -replace 'OutputDir=build\\windows\\installer', "OutputDir=$absoluteInstallerDir"
          
          # Write the modified script to a temporary file
          $tempIssFile = "windows\setup-wizard\installer_temp.iss"
          $issTemplate | Set-Content $tempIssFile
          
          Write-Host "=== Generated Installer Script Content ==="
          Write-Host "Key lines from the installer script:"
          $issTemplate.Split("`n") | Where-Object { $_ -match "(OutputDir|Source)" } | ForEach-Object { Write-Host "  $_" }
          Write-Host "=== End Installer Script Content ==="
          
          # Check if Inno Setup is available
          $innoSetupPath = "C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
          if (-not (Test-Path $innoSetupPath)) {
            Write-Host "Inno Setup not found at expected location. Searching for ISCC.exe..."
            $innoSetupPath = Get-Command "ISCC.exe" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Source
            if (-not $innoSetupPath) {
              Write-Host "ERROR: Inno Setup (ISCC.exe) not found"
              exit 1
            }
          }
          
          Write-Host "Using Inno Setup at: $innoSetupPath"
          Write-Host "Compiling installer with build type: Release"
          
          # Compile the installer
          & "$innoSetupPath" $tempIssFile
          
          if ($LASTEXITCODE -eq 0) {
            Write-Host "=== Installer Compilation Success ==="
            # Check both possible locations for the installer
            $expectedPath = "$installerDir\whph-setup.exe"
            $alternatePath = "build\windows\installer\whph-setup.exe"
            
            Write-Host "Checking for installer at expected path: $expectedPath"
            if (Test-Path $expectedPath) {
              $installerSize = (Get-Item $expectedPath).Length
              Write-Host "✅ Installer found at expected location: $expectedPath"
              Write-Host "   Size: $($installerSize / 1MB) MB"
            } elseif (Test-Path $alternatePath) {
              $installerSize = (Get-Item $alternatePath).Length
              Write-Host "✅ Installer found at alternate location: $alternatePath"
              Write-Host "   Size: $($installerSize / 1MB) MB"
              # Move it to the expected location
              Move-Item $alternatePath $expectedPath
              Write-Host "   Moved to expected location: $expectedPath"
            } else {
              Write-Host "❌ ERROR: Installer file not found at any expected location"
              Write-Host "Searching for whph-setup.exe in build directory..."
              $foundFiles = Get-ChildItem -Path "build" -Recurse -Filter "whph-setup.exe" -ErrorAction SilentlyContinue
              if ($foundFiles) {
                foreach ($file in $foundFiles) {
                  Write-Host "   Found: $($file.FullName)"
                  # Move the first one found to the expected location
                  Move-Item $file.FullName $expectedPath
                  Write-Host "   Moved to expected location: $expectedPath"
                  break
                }
              } else {
                Write-Host "   No whph-setup.exe files found in build directory"
                Write-Host "Contents of build directory:"
                if (Test-Path "build") {
                  Get-ChildItem "build" -Recurse | Where-Object { -not $_.PSIsContainer } | ForEach-Object { 
                    Write-Host "     $($_.FullName)" 
                  }
                }
                exit 1
              }
            }
          } else {
            Write-Host "ERROR: Installer creation failed with exit code: $LASTEXITCODE"
            exit 1
          }
          
          # Clean up temporary file
          if (Test-Path $tempIssFile) {
            Remove-Item $tempIssFile
          }

      - name: Verify and Display Build Output
        run: |
          Write-Host "=== Windows Build Output Verification ==="
          
          # Check portable build
          $portablePath = "build\windows\x64\runner\Release"
          if (Test-Path $portablePath) {
            Write-Host "✅ Portable build directory exists: $portablePath"
            
            # Get directory size
            $dirSize = (Get-ChildItem -Path $portablePath -Recurse -File | Measure-Object -Property Length -Sum).Sum
            $dirSizeMB = [math]::Round($dirSize / 1MB, 2)
            Write-Host "📦 Portable build size: $dirSize bytes ($dirSizeMB MB)"
            
            # Find and check main executable
            $executable = Join-Path $portablePath "whph.exe"
            if (Test-Path $executable) {
              $exeSize = (Get-Item $executable).Length
              $exeSizeMB = [math]::Round($exeSize / 1MB, 2)
              Write-Host "🚀 Executable size: $exeSize bytes ($exeSizeMB MB)"
              
              # Calculate SHA256 of executable
              $hash = Get-FileHash -Path $executable -Algorithm SHA256
              Write-Host "🔐 Executable SHA256: $($hash.Hash)"
              
              # Get modification time
              $modified = (Get-Item $executable).LastWriteTime
              Write-Host "⏰ Executable modified: $modified"
            } else {
              Write-Host "⚠️  Main executable not found at: $executable"
            }
            
            # List key files
            Write-Host "📁 Key build files:"
            Get-ChildItem -Path $portablePath -File | Where-Object { $_.Extension -in @('.exe', '.dll') } | ForEach-Object {
              $sizeKB = [math]::Round($_.Length / 1KB, 1)
              Write-Host "  - $($_.Name): $sizeKB KB"
            }
          } else {
            Write-Host "❌ ERROR: Portable build directory not found at: $portablePath"
          }
          
          # Check installer
          $installerPath = "build\windows\installer\whph-setup.exe"
          if (Test-Path $installerPath) {
            Write-Host "✅ Installer exists: $installerPath"
            
            $installerSize = (Get-Item $installerPath).Length
            $installerSizeMB = [math]::Round($installerSize / 1MB, 2)
            Write-Host "📦 Installer size: $installerSize bytes ($installerSizeMB MB)"
            
            # Calculate SHA256 of installer
            $installerHash = Get-FileHash -Path $installerPath -Algorithm SHA256
            Write-Host "🔐 Installer SHA256: $($installerHash.Hash)"
            
            # Get modification time
            $installerModified = (Get-Item $installerPath).LastWriteTime
            Write-Host "⏰ Installer modified: $installerModified"
          } else {
            Write-Host "❌ ERROR: Installer not found at: $installerPath"
          }
          
          Write-Host "=== Build Output Summary ==="
          if (Test-Path $portablePath) {
            Write-Host "Portable: $portablePath ($dirSizeMB MB)"
          }
          if (Test-Path $installerPath) {
            Write-Host "Installer: $installerPath ($installerSizeMB MB)"
          }

      - name: Get application version
        id: app_version
        run: PowerShell -File scripts/get_app_version.ps1

      - name: Upload Windows build artifact (Portable)
        uses: actions/upload-artifact@v4
        with:
          name: whph-v${{ env.APP_VERSION }}-windows-portable
          path: build\windows\x64\runner\Release\
          if-no-files-found: error
          compression-level: 6
          overwrite: false
          include-hidden-files: false

      - name: Upload Windows Installer
        uses: actions/upload-artifact@v4
        with:
          name: whph-v${{ env.APP_VERSION }}-windows-installer
          path: build\windows\installer\whph-setup.exe
          if-no-files-found: error
          compression-level: 0
          overwrite: false

      - name: Verify installer artifact exists
        run: |
          $installerPath = "build\windows\installer\whph-setup.exe"
          Write-Host "Final verification: Installer artifact exists at: $installerPath"
          if (Test-Path $installerPath) {
            $fileInfo = Get-Item $installerPath
            Write-Host "✅ SUCCESS: Installer ready for upload"
            Write-Host "   Path: $($fileInfo.FullName)"
            Write-Host "   Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
            Write-Host "   Created: $($fileInfo.CreationTime)"
            Write-Host "   Modified: $($fileInfo.LastWriteTime)"
          } else {
            Write-Host "❌ CRITICAL ERROR: Installer not found at expected location"
            Write-Host "This will cause the artifact upload to fail!"
            Write-Host "Contents of build\windows\installer directory:"
            if (Test-Path "build\windows\installer") {
              Get-ChildItem "build\windows\installer" | ForEach-Object { 
                Write-Host "  - $($_.Name) ($([math]::Round($_.Length / 1MB, 2)) MB)" 
              }
            } else {
              Write-Host "  Directory does not exist"
            }
            Write-Host "Contents of build\windows directory:"
            if (Test-Path "build\windows") {
              Get-ChildItem "build\windows" -Recurse | ForEach-Object { 
                if (-not $_.PSIsContainer) {
                  Write-Host "  File: $($_.FullName.Replace((Get-Location).Path, ''))"
                }
              }
            } else {
              Write-Host "  Directory does not exist"
            }
            exit 1
          }
