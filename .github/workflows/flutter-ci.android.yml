name: Flutter CI - Android

on:
  workflow_dispatch: # This is a trigger for manual workflow run
  push:
    tags:
      - 'v*.*.*'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v4
      with:
        submodules: true

    - name: Initialize acore submodule
      run: |
        git submodule update --init --recursive lib/corePackages/acore

    # Install dependencies
    - name: Set up Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.32.0'

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'adopt'

    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
    
    # Build for Android
    - name: Install RPS globally
      run: dart pub global activate rps

    - name: Capture Act Environment State
      run: bash scripts/act_environment_diagnostic.sh

    - name: Build for Android (Reproducible)
      run: rps release:android:reproducible
    
    - name: Verify and Display Build Output
      run: |
        APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
        echo "=== Android Build Output Verification ==="
        
        if [ -f "$APK_PATH" ]; then
          echo "✅ APK file exists: $APK_PATH"
          
          # Get file size
          FILE_SIZE=$(stat -c%s "$APK_PATH")
          FILE_SIZE_MB=$(awk "BEGIN {printf \"%.2f\", $FILE_SIZE / 1024 / 1024}")
          echo "📦 File size: $FILE_SIZE bytes ($FILE_SIZE_MB MB)"
          
          # Calculate SHA256
          SHA256=$(sha256sum "$APK_PATH" | cut -d' ' -f1)
          echo "🔐 SHA256: $SHA256"
          
          # Get file modification time
          MODIFIED=$(stat -c%y "$APK_PATH")
          echo "⏰ Modified: $MODIFIED"
          
          echo "=== Build Output Summary ==="
          echo "Path: $APK_PATH"
          echo "Size: $FILE_SIZE_MB MB"
          echo "Hash: $SHA256"
        else
          echo "❌ ERROR: APK file not found at $APK_PATH"
          echo "Build directory contents:"
          find build -name "*.apk" -type f 2>/dev/null || echo "No APK files found"
          exit 1
        fi
    
    # Publish the build artifacts
    - name: Get application version
      id: app_version
      run: bash scripts/get_app_version.sh

    - name: Upload Android build artifact
      uses: actions/upload-artifact@v4
      with:
        name: whph-v${{ env.APP_VERSION }}-android
        path: build/app/outputs/flutter-apk/app-release.apk

    - name: Upload Act Environment Diagnostic
      uses: actions/upload-artifact@v4
      with:
        name: act-environment-diagnostic
        path: act_diagnostic_*.txt
