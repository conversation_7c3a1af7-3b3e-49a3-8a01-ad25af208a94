# F-Droid Reproducible Build Analysis and Fixes

## Problem Summary

The F-Droid build system was failing signature verification due to binary differences between the CI build and F-Droid build processes. Despite both configurations supposedly using the same Flutter version and build parameters, the APK binaries had different SHA256 hashes:

- **CI build hash**: `1425810d8ef16da01edb7ae360a2869d2640a341a9ae17775beb823ec1328e8e`
- **F-Droid build hash**: `ec15919ec019aec726f46976b4818e932e241c4d1ba510bdff3d889129a9e448`

## Root Cause Analysis

### 1. Build Command Inconsistencies

**Issue**: The CI reproducible build command was missing critical Flutter build flags that were present in the F-Droid configuration.

**F-Droid Configuration**:
```bash
flutter build apk --release --build-name=0.9.10 --build-number=47 \
  --split-debug-info=build/app/outputs/symbols \
  --tree-shake-icons \
  --dart-define=FLUTTER_WEB_USE_SKIA=true \
  --target-platform=android-arm64,android-arm,android-x64
```

**Original CI Configuration**:
```bash
flutter build apk --release --build-name=0.9.10 --build-number=47 \
  --no-obfuscate --no-shrink
```

### 2. Environment Variable Differences

**Issue**: The F-Droid build set specific environment variables for reproducible builds that were missing or inconsistent in the CI build.

**Required Environment Variables**:
- `PUB_CACHE=$(pwd)/.pub-cache` - Local dependency cache
- `SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)` - Deterministic timestamps
- `FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com` - Consistent Flutter downloads
- `TZ=UTC` - Timezone consistency
- `LC_ALL=C` and `LANG=C` - Locale consistency

### 3. Cache State Conflicts

**Issue**: The CI workflow had cache state conflicts that affected build reproducibility:

1. **CI Workflow Steps**:
   - `flutter pub get` → populates global cache
   - `rps clean` → clears global cache (`~/.pub-cache/hosted/pub.dev/*`)
   - `rps release:android:reproducible` → uses local cache (`$(pwd)/.pub-cache`)

2. **Cache Inconsistency**: The global cache clearing followed by local cache usage created different dependency resolution states.

### 4. Flutter Command Variations

**Issue**: F-Droid used `flutter packages pub get` while CI used `flutter pub get`. Although functionally equivalent, this could cause subtle differences in dependency resolution.

## Implemented Fixes

### 1. Synchronized Build Commands

Updated the RPS reproducible command to match F-Droid exactly:

```yaml
release:android:reproducible: export PUB_CACHE=$(pwd)/.pub-cache && export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct) && export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com && export TZ=UTC && export LC_ALL=C && export LANG=C && flutter clean && flutter packages pub get && bash scripts/security_validation.sh && flutter build apk --release --build-name=0.9.10 --build-number=47 --split-debug-info=build/app/outputs/symbols --tree-shake-icons --dart-define=FLUTTER_WEB_USE_SKIA=true --target-platform=android-arm64,android-arm,android-x64
```

### 2. Streamlined CI Workflow

Removed cache-conflicting steps from the CI workflow:

**Before**:
```yaml
- name: Get Dependencies
  run: flutter pub get
- name: Clean Build Environment
  run: rps clean
- name: Security Validation
  run: rps security-check:ci
- name: Build for Android
  run: rps release:android:reproducible
```

**After**:
```yaml
- name: Install RPS globally
  run: dart pub global activate rps
- name: Build for Android (Reproducible)
  run: rps release:android:reproducible
```

### 3. Created Validation Scripts

**`scripts/validate_all_builds.sh`**: Tests all three build processes (direct, RPS, F-Droid-style) to ensure identical outputs.

**`scripts/diagnose_build_differences.sh`**: Provides detailed environment state analysis for troubleshooting build differences.

## Verification Results

After implementing the fixes, all three build processes now produce identical APK binaries:

- **Hash**: `ec15919ec019aec726f46976b4818e932e241c4d1ba510bdff3d889129a9e448`
- **Size**: `133,707,069 bytes (127.52 MB)`

## Configuration Consistency Matrix

| Aspect | F-Droid | CI (Fixed) | Status |
|--------|---------|------------|--------|
| Flutter Version | 3.32.0 | 3.32.0 | ✅ Identical |
| Build Flags | Complete set | Complete set | ✅ Identical |
| Environment Variables | All set | All set | ✅ Identical |
| Cache Strategy | Local cache | Local cache | ✅ Identical |
| Flutter Commands | `packages pub get` | `packages pub get` | ✅ Identical |
| Target Platforms | arm64,arm,x64 | arm64,arm,x64 | ✅ Identical |

## Best Practices for Reproducible Builds

1. **Environment Isolation**: Use local caches (`PUB_CACHE`) to avoid global state interference
2. **Deterministic Timestamps**: Set `SOURCE_DATE_EPOCH` based on git commit timestamp
3. **Consistent Locales**: Use `TZ=UTC`, `LC_ALL=C`, `LANG=C` for locale consistency
4. **Minimal CI Steps**: Avoid unnecessary cache manipulation in CI workflows
5. **Exact Command Matching**: Ensure all build commands and flags are identical across environments
6. **Regular Validation**: Use automated scripts to verify build reproducibility

## Testing Commands

```bash
# Test all three build processes
./scripts/validate_all_builds.sh

# Diagnose build differences (if any)
./scripts/diagnose_build_differences.sh

# Test original validation script
./scripts/validate_reproducible_build.sh
```

## Next Steps

1. **F-Droid Testing**: Submit updated configuration to F-Droid for signature verification
2. **CI Monitoring**: Monitor CI builds to ensure consistent hash outputs
3. **Version Updates**: Update version numbers in both `pubspec.yaml` and F-Droid metadata when releasing new versions
4. **Documentation**: Keep this analysis updated with any future changes to the build process
