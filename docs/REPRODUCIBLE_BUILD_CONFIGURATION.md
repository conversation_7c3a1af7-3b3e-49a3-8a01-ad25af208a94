# Reproducible Build Configuration

This document describes the comprehensive reproducible build setup for the WHPH Flutter application, ensuring identical builds across local development, CI/CD, and F-Droid environments.

## Overview

The WHPH project implements fully reproducible builds with the following key features:

- **Identical Build Outputs**: All builds produce the same APK/binary with matching SHA256 hashes
- **Centralized Version Management**: Single source of truth for version extraction
- **Comprehensive Verification**: Automated build output verification with detailed metrics
- **Cross-Platform Support**: Works on Linux, Windows, and Android builds

## Build Environment Synchronization

### Environment Variables

All builds use identical environment variables to ensure reproducibility:

```bash
# Set in all build environments
export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)  # Deterministic timestamps
export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com  # Consistent downloads
export TZ=UTC  # Timezone consistency
export LC_ALL=C  # Locale consistency
export LANG=C  # Language consistency
```

### Build Flags

All builds use identical Flutter build flags:

```bash
--release              # Release mode
--build-name=0.9.10   # Fixed version name
--build-number=47     # Fixed build number
--no-obfuscate        # Disable obfuscation for reproducibility
--no-shrink           # Disable code shrinking for reproducibility
```

## Version Management

### Centralized Version Extraction

The project uses dedicated scripts for version extraction:

- **Bash Script**: `scripts/get_app_version.sh` (Linux/macOS)
- **PowerShell Script**: `scripts/get_app_version.ps1` (Windows)

Both scripts:
- Extract version from `pubspec.yaml`
- Validate semantic versioning format
- Set environment variables for CI/CD
- Provide consistent output format

### Usage

```bash
# Local development
rps get-version

# CI/CD (automatically called in workflows)
bash scripts/get_app_version.sh
PowerShell -File scripts/get_app_version.ps1
```

## Build Output Verification

### Comprehensive Verification Steps

All workflows include detailed build output verification:

#### Android Build Verification

```bash
=== Android Build Output Verification ===
✅ APK file exists: build/app/outputs/flutter-apk/app-release.apk
📦 File size: 85432123 bytes (81.45 MB)
🔐 SHA256: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2
⏰ Modified: 2024-01-15 10:30:45.123456789 +0000
```

#### Linux Build Verification

```bash
=== Linux Build Output Verification ===
✅ Build directory exists: build/linux/x64/release/bundle
📦 Build size: 45678901 bytes (43.55 MB)
📁 Build contents:
  - whph: 25.3 MB
  - libgtk.so: 2.1 MB
  - libother.so: 1.8 MB
🔐 Executable SHA256: b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3
⏰ Modified: 2024-01-15 10:35:21.987654321 +0000
```

#### Windows Build Verification

```bash
=== Windows Build Output Verification ===
✅ Portable build directory exists: build\windows\x64\runner\Release
📦 Portable build size: 67890123 bytes (64.72 MB)
🚀 Executable size: 45678901 bytes (43.55 MB)
🔐 Executable SHA256: c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4
⏰ Executable modified: 2024-01-15 10:40:12.123456789 +0000
✅ Installer exists: build\windows\installer\whph-setup.exe
📦 Installer size: 34567890 bytes (32.98 MB)
🔐 Installer SHA256: d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4i5
⏰ Installer modified: 2024-01-15 10:41:05.987654321 +0000
```

### Verification Data Points

Each build verification includes:

1. **File/Directory Existence**: Confirms expected outputs exist
2. **Size Metrics**: File sizes in bytes and human-readable format
3. **SHA256 Hashes**: Cryptographic verification of file integrity
4. **Timestamps**: Build completion and modification times
5. **Content Listing**: Key files and their individual sizes

## CI/CD Integration

### Workflow Files

- **Android**: `.github/workflows/flutter-ci.android.yml`
- **Linux**: `.github/workflows/flutter-ci.linux.yml`
- **Windows**: `.github/workflows/flutter-ci.windows.yml`

### Common Workflow Steps

1. **Environment Setup**: Install Flutter, dependencies, and tools
2. **Version Extraction**: Run platform-specific version script
3. **Build Execution**: Use `rps` commands for consistent builds
4. **Output Verification**: Display detailed build metrics
5. **Artifact Upload**: Upload with version-tagged names

### Artifact Naming Convention

All artifacts use the format: `whph-v{VERSION}-{PLATFORM}`

Examples:
- `whph-v0.9.10-android`
- `whph-v0.9.10-linux`
- `whph-v0.9.10-windows-portable`
- `whph-v0.9.10-windows-installer`

## Local Testing

### Reproducible Build Testing

Use the local test script to verify builds match CI output:

```bash
# Run reproducible build test
bash scripts/test_reproducible_build.sh
```

This script:
- Sets identical environment variables as CI
- Runs the same build commands
- Displays verification output in CI format
- Shows build hash for comparison

### RPS Commands

Common development commands:

```bash
# Clean build environment
rps clean

# Build for different platforms
rps release:android:reproducible
rps release:linux
rps release:windows

# Extract version
rps get-version

# Run security validation
rps security-check
```

## F-Droid Integration

### Metadata Configuration

F-Droid builds use the same reproducible build configuration:

- Same environment variables
- Same build flags
- Same version extraction
- Deterministic source timestamps

### Build Verification

F-Droid builds can be verified by:

1. Running local reproducible build
2. Comparing SHA256 hashes
3. Validating file sizes
4. Checking build timestamps

## Security Considerations

### Build Integrity

- All builds include cryptographic verification (SHA256)
- Deterministic timestamps prevent timing attacks
- Consistent environment variables prevent variance
- No code obfuscation for transparency

### Validation Steps

1. **Security Scanning**: `rps security-check:ci`
2. **Dependency Verification**: Locked versions in `pubspec.lock`
3. **Build Environment**: Controlled CI environments
4. **Hash Verification**: SHA256 checksums for all outputs

## Troubleshooting

### Common Issues

1. **Hash Mismatch**: Check environment variables and build flags
2. **Size Differences**: Verify Flutter version and dependencies
3. **Missing Artifacts**: Check build output verification logs
4. **Version Extraction**: Ensure `pubspec.yaml` format is correct

### Debug Commands

```bash
# Check environment
env | grep -E "(SOURCE_DATE_EPOCH|TZ|LC_ALL|LANG|FLUTTER_STORAGE_BASE_URL)"

# Verify build output
ls -la build/app/outputs/flutter-apk/
sha256sum build/app/outputs/flutter-apk/app-release.apk

# Test version extraction
rps get-version
```

## Maintenance

### Version Updates

1. Update version in `pubspec.yaml`
2. Update build flags in reproducible build commands
3. Test local build matches CI output
4. Update documentation if needed

### Dependency Updates

1. Update `pubspec.yaml` dependencies
2. Run `flutter pub get` to update `pubspec.lock`
3. Test reproducible builds
4. Verify all workflows still pass

## Conclusion

The WHPH project's reproducible build system ensures:

- **Consistency**: Identical builds across all environments
- **Transparency**: Detailed verification and logging
- **Security**: Cryptographic verification and validation
- **Maintainability**: Centralized configuration and documentation

This configuration enables trustworthy releases and supports security-conscious distribution channels like F-Droid.

### 6. Enhanced Build Output Verification

Added comprehensive build output verification to all workflows:

#### Android Workflow Output:
```
=== Android Build Output Verification ===
✅ APK file exists: build/app/outputs/flutter-apk/app-release.apk
📦 File size: 136321085 bytes (130.01 MB)
🔐 SHA256: ca8d54cd9e23640056efdad109a5b5e903ebce0d2a692035f0e3e36e57665b3f
⏰ Modified: 2025-07-06 15:17:28.034682603 +0300
=== Build Output Summary ===
Path: build/app/outputs/flutter-apk/app-release.apk
Size: 130.01 MB
Hash: ca8d54cd9e23640056efdad109a5b5e903ebce0d2a692035f0e3e36e57665b3f
```

#### Linux Workflow Output:
```
=== Linux Build Output Verification ===
✅ Build directory exists: build/linux/x64/release/bundle
📦 Build size: [size] bytes ([size] MB)
📁 Build contents: [executable and library files]
🔐 Executable SHA256: [hash]
```

#### Windows Workflow Output:
```
=== Windows Build Output Verification ===
✅ Portable build directory exists: build\windows\x64\runner\Release
📦 Portable build size: [size] bytes ([size] MB)
🚀 Executable size: [size] bytes ([size] MB)
🔐 Executable SHA256: [hash]
✅ Installer exists: build\windows\installer\whph-setup.exe
📦 Installer size: [size] bytes ([size] MB)
🔐 Installer SHA256: [hash]
```

## How to Verify

### Run Local Reproducible Build
```bash
# Using the rps script
rps release:android:reproducible

# Or using the test script
./scripts/test_reproducible_build.sh

# Or manually
export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com
export TZ=UTC
export LC_ALL=C
export LANG=C
flutter clean
flutter pub get
bash scripts/security_validation.sh
flutter build apk --release --build-name=0.9.10 --build-number=47 --no-obfuscate --no-shrink
```

### Check APK Hash
```bash
sha256sum build/app/outputs/flutter-apk/app-release.apk
```

### Test CI Build Locally
```bash
rps test:ci:android
```

## Expected Results

With the current configuration (commit `1751796427`), both local and CI builds should produce:
- **SHA256**: `ca8d54cd9e23640056efdad109a5b5e903ebce0d2a692035f0e3e36e57665b3f`
- **File size**: ~136.3MB

## Files Modified

1. `.github/workflows/flutter-ci.android.yml` - Updated CI build configuration
2. `.github/workflows/flutter-ci.linux.yml` - Updated CI build configuration  
3. `.github/workflows/flutter-ci.windows.yml` - Updated CI build configuration
4. `pubspec.yaml` - Updated `release:android:reproducible` script and added `get-version`
5. `scripts/test_reproducible_build.sh` - New test script for verification
6. `scripts/get_app_version.sh` - New script for extracting app version (Linux/macOS)
7. `scripts/get_app_version.ps1` - New script for extracting app version (Windows)

## Notes

- The SOURCE_DATE_EPOCH is calculated from the git commit timestamp to ensure deterministic builds
- All environment variables are set to ensure consistent build environment
- Font tree-shaking warnings are expected and normal
- The build produces unsigned APKs for reproducibility

## F-Droid Compatibility

This configuration is also compatible with F-Droid's reproducible build requirements. The F-Droid metadata in `android/fdroid/metadata/me.ahmetcetinkaya.whph.yml` uses the same build flags and environment variables.
