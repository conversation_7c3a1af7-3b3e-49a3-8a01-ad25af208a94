Categories:
  - Science & Education
License: GPL-3.0-or-later
AuthorName: Ahmet Çetinkaya
AuthorEmail: <EMAIL>
WebSite: https://whph.ahmetcetinkaya.me
SourceCode: https://github.com/ahmet-cetinkaya/whph
IssueTracker: https://github.com/ahmet-cetinkaya/whph/issues
Donate: https://ahmetcetinkaya.me/donate

AutoName: Work Hard Play Hard

RepoType: git
Repo: https://github.com/ahmet-cetinkaya/whph
Binaries: https://github.com/ahmet-cetinkaya/whph/releases/download/v%v/whph-v%v-android.apk

Builds:
  - versionName: 0.9.10
    versionCode: 47
    commit: b04895f7577310c5582aafa55733492a7eec7bfe
    submodules: true
    output: build/app/outputs/flutter-apk/app-release.apk
    srclibs:
      - flutter@3.32.0
    rm:
      - ios
      - linux
      - macos
      - web
      - windows
    prebuild:
      - export flutterVersion=$(bash scripts/get_flutter_version.sh)
      - '[[ $flutterVersion ]] || exit 1'
      - pushd $$flutter$$
      - git checkout -f $flutterVersion
      - popd
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
      - export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com
      - export TZ=UTC
      - export LC_ALL=C
      - export LANG=C
      - $$flutter$$/bin/flutter config --no-analytics
      - $$flutter$$/bin/flutter clean
      - $$flutter$$/bin/flutter packages pub get
    scandelete:
      - android
      - .pub-cache
    build:
      - export PUB_CACHE=$(pwd)/.pub-cache
      - export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
      - export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com
      - export TZ=UTC
      - export LC_ALL=C
      - export LANG=C
      - $$flutter$$/bin/flutter clean
      - $$flutter$$/bin/flutter packages pub get
      - $$flutter$$/bin/flutter build apk --release --build-name=0.9.10 --build-number=47
        --split-debug-info=build/app/outputs/symbols --tree-shake-icons --dart-define=FLUTTER_WEB_USE_SKIA=true
        --target-platform=android-arm64,android-arm,android-x64

AllowedAPKSigningKeys: 4b0de165375bb1179fbee37fbd70de03813284529e0b0c5d3ce5e794f03aa0ae

AutoUpdateMode: Version
UpdateCheckMode: Tags
UpdateCheckData: pubspec.yaml|version:\s.+\+(\d+)|.|version:\s(.+)\+
CurrentVersion: 0.9.10+47
CurrentVersionCode: 47
