#!/bin/bash

# WHPH - Act Environment Diagnostic Script
# This script captures the environment state when running inside act (GitHub Actions runner simulation)
# Usage: Called from GitHub Actions workflow to diagnose act vs local differences

set -e

echo "🔍 Act Environment Diagnostic"
echo "============================"
echo "Timestamp: $(date)"
echo "PWD: $(pwd)"
echo ""

# Colors for output (may not work in act environment)
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status (without colors in case they don't work)
print_status() {
    local message=$2
    echo ">>> $message"
}

# Create diagnostic output file
DIAGNOSTIC_FILE="act_diagnostic_$(date +%Y%m%d_%H%M%S).txt"

{
    echo "=== Act Environment Diagnostic Report ==="
    echo "Generated: $(date)"
    echo "Hostname: $(hostname)"
    echo "User: $(whoami)"
    echo "UID: $(id -u)"
    echo "GID: $(id -g)"
    echo ""

    echo "=== System Information ==="
    echo "OS: $(uname -a)"
    echo "Distribution: $(cat /etc/os-release 2>/dev/null | head -5 || echo 'Not available')"
    echo "Architecture: $(uname -m)"
    echo "Kernel: $(uname -r)"
    echo ""

    echo "=== Environment Variables ==="
    echo "HOME: ${HOME:-'NOT_SET'}"
    echo "USER: ${USER:-'NOT_SET'}"
    echo "PATH: ${PATH:-'NOT_SET'}"
    echo "SHELL: ${SHELL:-'NOT_SET'}"
    echo "PWD: ${PWD:-'NOT_SET'}"
    echo ""

    echo "=== GitHub Actions Environment ==="
    echo "GITHUB_WORKSPACE: ${GITHUB_WORKSPACE:-'NOT_SET'}"
    echo "GITHUB_REPOSITORY: ${GITHUB_REPOSITORY:-'NOT_SET'}"
    echo "GITHUB_SHA: ${GITHUB_SHA:-'NOT_SET'}"
    echo "GITHUB_REF: ${GITHUB_REF:-'NOT_SET'}"
    echo "GITHUB_ACTIONS: ${GITHUB_ACTIONS:-'NOT_SET'}"
    echo "RUNNER_OS: ${RUNNER_OS:-'NOT_SET'}"
    echo "RUNNER_ARCH: ${RUNNER_ARCH:-'NOT_SET'}"
    echo "RUNNER_TEMP: ${RUNNER_TEMP:-'NOT_SET'}"
    echo "RUNNER_TOOL_CACHE: ${RUNNER_TOOL_CACHE:-'NOT_SET'}"
    echo ""

    echo "=== Build Environment Variables ==="
    echo "PUB_CACHE: ${PUB_CACHE:-'NOT_SET'}"
    echo "SOURCE_DATE_EPOCH: ${SOURCE_DATE_EPOCH:-'NOT_SET'}"
    echo "FLUTTER_STORAGE_BASE_URL: ${FLUTTER_STORAGE_BASE_URL:-'NOT_SET'}"
    echo "TZ: ${TZ:-'NOT_SET'}"
    echo "LC_ALL: ${LC_ALL:-'NOT_SET'}"
    echo "LANG: ${LANG:-'NOT_SET'}"
    echo "JAVA_HOME: ${JAVA_HOME:-'NOT_SET'}"
    echo "ANDROID_HOME: ${ANDROID_HOME:-'NOT_SET'}"
    echo "ANDROID_SDK_ROOT: ${ANDROID_SDK_ROOT:-'NOT_SET'}"
    echo ""

    echo "=== Git Information ==="
    echo "Git version: $(git --version 2>/dev/null || echo 'Not available')"
    echo "Current directory: $(pwd)"
    echo "Git repository: $(git remote get-url origin 2>/dev/null || echo 'Not a git repository or no origin')"
    echo "Current branch: $(git branch --show-current 2>/dev/null || echo 'Not available')"
    echo "Last commit: $(git log -1 --format='%H %ct %s' 2>/dev/null || echo 'Not available')"
    echo "SOURCE_DATE_EPOCH calculation: $(git log -1 --format=%ct 2>/dev/null || echo 'Not available')"
    echo "Git status: $(git status --porcelain 2>/dev/null | wc -l) modified files"
    echo ""

    echo "=== Flutter Information ==="
    echo "Flutter version:"
    flutter --version 2>&1 || echo "Flutter not available"
    echo ""
    echo "Flutter doctor:"
    flutter doctor -v 2>&1 || echo "Flutter doctor failed"
    echo ""

    echo "=== Java Information ==="
    echo "Java version:"
    java -version 2>&1 || echo "Java not available"
    echo ""
    echo "JAVA_HOME contents:"
    ls -la "${JAVA_HOME:-/usr/lib/jvm/default-java}" 2>/dev/null || echo "JAVA_HOME not accessible"
    echo ""

    echo "=== Android SDK Information ==="
    echo "ANDROID_HOME contents:"
    ls -la "${ANDROID_HOME:-/usr/local/lib/android/sdk}" 2>/dev/null || echo "ANDROID_HOME not accessible"
    echo ""
    echo "Android SDK tools:"
    which adb 2>/dev/null || echo "adb not found"
    which sdkmanager 2>/dev/null || echo "sdkmanager not found"
    echo ""

    echo "=== File System Information ==="
    echo "Current directory contents:"
    ls -la
    echo ""
    echo "Home directory contents:"
    ls -la ~ 2>/dev/null || echo "Home directory not accessible"
    echo ""

    echo "=== Cache Information ==="
    echo "Global pub cache (~/.pub-cache):"
    if [ -d ~/.pub-cache ]; then
        echo "  Exists: Yes"
        echo "  Size: $(du -sh ~/.pub-cache 2>/dev/null | cut -f1 || echo 'Cannot calculate')"
        echo "  Contents:"
        ls -la ~/.pub-cache 2>/dev/null | head -10
    else
        echo "  Exists: No"
    fi
    echo ""

    echo "Local pub cache (./.pub-cache):"
    if [ -d ./.pub-cache ]; then
        echo "  Exists: Yes"
        echo "  Size: $(du -sh ./.pub-cache 2>/dev/null | cut -f1 || echo 'Cannot calculate')"
        echo "  Contents:"
        ls -la ./.pub-cache 2>/dev/null | head -10
    else
        echo "  Exists: No"
    fi
    echo ""

    echo "=== Build Directory Information ==="
    if [ -d build ]; then
        echo "Build directory exists"
        echo "Size: $(du -sh build 2>/dev/null | cut -f1 || echo 'Cannot calculate')"
        echo "Contents:"
        find build -type f -name "*.apk" 2>/dev/null || echo "No APK files found"
    else
        echo "Build directory does not exist"
    fi
    echo ""

    echo "=== Network and DNS ==="
    echo "DNS resolution test:"
    nslookup storage.googleapis.com 2>/dev/null || echo "DNS resolution failed"
    echo ""

    echo "=== Process Information ==="
    echo "Running processes:"
    ps aux | head -10 2>/dev/null || echo "Cannot list processes"
    echo ""

    echo "=== Disk Space ==="
    echo "Disk usage:"
    df -h 2>/dev/null || echo "Cannot get disk usage"
    echo ""

    echo "=== Memory Information ==="
    echo "Memory usage:"
    free -h 2>/dev/null || echo "Cannot get memory info"
    echo ""

} > "$DIAGNOSTIC_FILE"

print_status $GREEN "Diagnostic information saved to: $DIAGNOSTIC_FILE"

# Also output key information to console
echo ""
echo "=== Key Environment Information ==="
echo "Hostname: $(hostname)"
echo "User: $(whoami)"
echo "PWD: $(pwd)"
echo "HOME: ${HOME:-'NOT_SET'}"
echo "GITHUB_WORKSPACE: ${GITHUB_WORKSPACE:-'NOT_SET'}"
echo "Flutter version: $(flutter --version 2>/dev/null | head -1 || echo 'Not available')"
echo "Git last commit: $(git log -1 --format='%H %ct' 2>/dev/null || echo 'Not available')"
echo "SOURCE_DATE_EPOCH would be: $(git log -1 --format=%ct 2>/dev/null || echo 'Not available')"

# Upload diagnostic file as artifact if possible
if [ -f "$DIAGNOSTIC_FILE" ]; then
    echo ""
    echo "Diagnostic file created: $DIAGNOSTIC_FILE"
    echo "File size: $(stat -c%s "$DIAGNOSTIC_FILE" 2>/dev/null || echo 'Unknown') bytes"
fi
