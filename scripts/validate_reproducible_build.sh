#!/bin/bash

# WHPH - Reproducible Build Validation Script
# This script validates that the F-Droid and CI build configurations produce identical APK binaries
# Usage: ./validate_reproducible_build.sh

set -e

echo "🔍 WHPH Reproducible Build Validation"
echo "====================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to calculate SHA256 hash
calculate_hash() {
    local file=$1
    if [ -f "$file" ]; then
        sha256sum "$file" | cut -d' ' -f1
    else
        echo "FILE_NOT_FOUND"
    fi
}

# Clean up any previous builds
print_status $YELLOW "🧹 Cleaning previous builds..."
flutter clean
rm -rf build/
rm -rf .pub-cache/

# Build 1: Using RPS reproducible command (CI-style)
print_status $BLUE "🔨 Build 1: CI-style reproducible build..."
rps release:android:reproducible

# Backup the first build
APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
if [ -f "$APK_PATH" ]; then
    cp "$APK_PATH" "build1-app-release.apk"
    BUILD1_HASH=$(calculate_hash "build1-app-release.apk")
    BUILD1_SIZE=$(stat -c%s "build1-app-release.apk")
    print_status $GREEN "✅ Build 1 completed - Hash: $BUILD1_HASH"
else
    print_status $RED "❌ Build 1 failed - APK not found"
    exit 1
fi

# Clean for second build
print_status $YELLOW "🧹 Cleaning for second build..."
flutter clean
rm -rf build/
rm -rf .pub-cache/

# Build 2: Using F-Droid-style commands
print_status $BLUE "🔨 Build 2: F-Droid-style reproducible build..."

# Set environment variables (matching F-Droid configuration)
export PUB_CACHE=$(pwd)/.pub-cache
export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com
export TZ=UTC
export LC_ALL=C
export LANG=C

# Run F-Droid-style build commands
flutter config --no-analytics
flutter clean
flutter packages pub get
flutter build apk --release --build-name=0.9.10 --build-number=47 \
    --split-debug-info=build/app/outputs/symbols \
    --tree-shake-icons \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --target-platform=android-arm64,android-arm,android-x64

# Check second build
if [ -f "$APK_PATH" ]; then
    cp "$APK_PATH" "build2-app-release.apk"
    BUILD2_HASH=$(calculate_hash "build2-app-release.apk")
    BUILD2_SIZE=$(stat -c%s "build2-app-release.apk")
    print_status $GREEN "✅ Build 2 completed - Hash: $BUILD2_HASH"
else
    print_status $RED "❌ Build 2 failed - APK not found"
    exit 1
fi

# Compare builds
print_status $YELLOW "🔍 Comparing builds..."
echo ""
echo "Build 1 (CI-style):"
echo "  Hash: $BUILD1_HASH"
echo "  Size: $BUILD1_SIZE bytes"
echo ""
echo "Build 2 (F-Droid-style):"
echo "  Hash: $BUILD2_HASH"
echo "  Size: $BUILD2_SIZE bytes"
echo ""

if [ "$BUILD1_HASH" = "$BUILD2_HASH" ]; then
    print_status $GREEN "🎉 SUCCESS: Builds are identical!"
    print_status $GREEN "✅ F-Droid signature verification should now pass"
    
    # Clean up temporary files
    rm -f build1-app-release.apk build2-app-release.apk
    
    echo ""
    echo "📋 Validation Summary:"
    echo "  - Both builds produce identical APK binaries"
    echo "  - SHA256 hashes match: $BUILD1_HASH"
    echo "  - File sizes match: $BUILD1_SIZE bytes"
    echo "  - F-Droid reproducible build requirements satisfied"
    
else
    print_status $RED "❌ FAILURE: Builds are different!"
    print_status $RED "⚠️  F-Droid signature verification will fail"
    
    echo ""
    echo "📋 Differences found:"
    echo "  - Build 1 hash: $BUILD1_HASH"
    echo "  - Build 2 hash: $BUILD2_HASH"
    echo "  - Size difference: $((BUILD2_SIZE - BUILD1_SIZE)) bytes"
    echo ""
    echo "🔧 Troubleshooting steps:"
    echo "  1. Check for remaining environment variable differences"
    echo "  2. Verify Flutter version consistency"
    echo "  3. Check for timestamp-related issues"
    echo "  4. Review Android build.gradle reproducible settings"
    
    # Keep files for analysis
    print_status $YELLOW "📁 Keeping build files for analysis:"
    echo "  - build1-app-release.apk (CI-style build)"
    echo "  - build2-app-release.apk (F-Droid-style build)"
    
    exit 1
fi

print_status $GREEN "🔗 Next steps:"
echo "  1. Test the updated configuration with F-Droid build system"
echo "  2. Verify that F-Droid signature verification passes"
echo "  3. Monitor future builds for consistency"
