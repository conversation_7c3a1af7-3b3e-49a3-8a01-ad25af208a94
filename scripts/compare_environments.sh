#!/bin/bash

# WHPH - Environment Comparison Script
# This script compares local environment with act environment to identify build differences
# Usage: ./compare_environments.sh

set -e

echo "🔍 Environment Comparison Analysis"
echo "================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Create local environment diagnostic
print_status $BLUE "📋 Capturing local environment state..."

LOCAL_DIAGNOSTIC="local_diagnostic_$(date +%Y%m%d_%H%M%S).txt"

{
    echo "=== Local Environment Diagnostic Report ==="
    echo "Generated: $(date)"
    echo "Hostname: $(hostname)"
    echo "User: $(whoami)"
    echo "UID: $(id -u)"
    echo "GID: $(id -g)"
    echo ""

    echo "=== System Information ==="
    echo "OS: $(uname -a)"
    echo "Distribution: $(cat /etc/os-release 2>/dev/null | head -5 || echo 'Not available')"
    echo "Architecture: $(uname -m)"
    echo "Kernel: $(uname -r)"
    echo ""

    echo "=== Environment Variables ==="
    echo "HOME: ${HOME:-'NOT_SET'}"
    echo "USER: ${USER:-'NOT_SET'}"
    echo "PATH: ${PATH:-'NOT_SET'}"
    echo "SHELL: ${SHELL:-'NOT_SET'}"
    echo "PWD: ${PWD:-'NOT_SET'}"
    echo ""

    echo "=== Build Environment Variables ==="
    echo "PUB_CACHE: ${PUB_CACHE:-'NOT_SET'}"
    echo "SOURCE_DATE_EPOCH: ${SOURCE_DATE_EPOCH:-'NOT_SET'}"
    echo "FLUTTER_STORAGE_BASE_URL: ${FLUTTER_STORAGE_BASE_URL:-'NOT_SET'}"
    echo "TZ: ${TZ:-'NOT_SET'}"
    echo "LC_ALL: ${LC_ALL:-'NOT_SET'}"
    echo "LANG: ${LANG:-'NOT_SET'}"
    echo "JAVA_HOME: ${JAVA_HOME:-'NOT_SET'}"
    echo "ANDROID_HOME: ${ANDROID_HOME:-'NOT_SET'}"
    echo "ANDROID_SDK_ROOT: ${ANDROID_SDK_ROOT:-'NOT_SET'}"
    echo ""

    echo "=== Git Information ==="
    echo "Git version: $(git --version 2>/dev/null || echo 'Not available')"
    echo "Current directory: $(pwd)"
    echo "Git repository: $(git remote get-url origin 2>/dev/null || echo 'Not a git repository or no origin')"
    echo "Current branch: $(git branch --show-current 2>/dev/null || echo 'Not available')"
    echo "Last commit: $(git log -1 --format='%H %ct %s' 2>/dev/null || echo 'Not available')"
    echo "SOURCE_DATE_EPOCH calculation: $(git log -1 --format=%ct 2>/dev/null || echo 'Not available')"
    echo "Git status: $(git status --porcelain 2>/dev/null | wc -l) modified files"
    echo ""

    echo "=== Flutter Information ==="
    echo "Flutter version:"
    flutter --version 2>&1 || echo "Flutter not available"
    echo ""
    echo "Flutter location: $(which flutter 2>/dev/null || echo 'Not found')"
    echo "Flutter root: ${FLUTTER_ROOT:-'NOT_SET'}"
    echo ""

    echo "=== Java Information ==="
    echo "Java version:"
    java -version 2>&1 || echo "Java not available"
    echo ""
    echo "Java location: $(which java 2>/dev/null || echo 'Not found')"
    echo "JAVA_HOME: ${JAVA_HOME:-'NOT_SET'}"
    echo ""

    echo "=== Android SDK Information ==="
    echo "ANDROID_HOME: ${ANDROID_HOME:-'NOT_SET'}"
    echo "ANDROID_SDK_ROOT: ${ANDROID_SDK_ROOT:-'NOT_SET'}"
    echo "adb location: $(which adb 2>/dev/null || echo 'Not found')"
    echo "sdkmanager location: $(which sdkmanager 2>/dev/null || echo 'Not found')"
    echo ""

    echo "=== Cache Information ==="
    echo "Global pub cache (~/.pub-cache):"
    if [ -d ~/.pub-cache ]; then
        echo "  Exists: Yes"
        echo "  Size: $(du -sh ~/.pub-cache 2>/dev/null | cut -f1 || echo 'Cannot calculate')"
    else
        echo "  Exists: No"
    fi
    echo ""

    echo "Local pub cache (./.pub-cache):"
    if [ -d ./.pub-cache ]; then
        echo "  Exists: Yes"
        echo "  Size: $(du -sh ./.pub-cache 2>/dev/null | cut -f1 || echo 'Cannot calculate')"
    else
        echo "  Exists: No"
    fi
    echo ""

    echo "=== Dart/Flutter Dependencies ==="
    echo "pubspec.lock hash: $(sha256sum pubspec.lock 2>/dev/null | cut -d' ' -f1 || echo 'Not available')"
    echo "Flutter SDK hash: $(find $(flutter --version | grep 'Flutter' | awk '{print $NF}' | tr -d '()') -name '*.dart' -exec cat {} \; 2>/dev/null | sha256sum | cut -d' ' -f1 2>/dev/null || echo 'Cannot calculate')"
    echo ""

} > "$LOCAL_DIAGNOSTIC"

print_status $GREEN "✅ Local environment diagnostic saved to: $LOCAL_DIAGNOSTIC"

# Run act to generate act environment diagnostic
print_status $BLUE "🐳 Running act to capture containerized environment..."

# Run the CI workflow with act to generate the act diagnostic
if command -v act > /dev/null 2>&1; then
    print_status $YELLOW "Running act workflow..."
    rps test:ci:android || print_status $YELLOW "Act run completed (may have failed, but diagnostic should be generated)"
    
    # Look for act diagnostic files
    ACT_DIAGNOSTIC=$(ls -t act_diagnostic_*.txt 2>/dev/null | head -1 || echo "")
    
    if [ -n "$ACT_DIAGNOSTIC" ] && [ -f "$ACT_DIAGNOSTIC" ]; then
        print_status $GREEN "✅ Act environment diagnostic found: $ACT_DIAGNOSTIC"
        
        # Compare key differences
        print_status $BLUE "🔍 Analyzing key differences..."
        
        echo ""
        echo "=== ENVIRONMENT COMPARISON ANALYSIS ==="
        echo ""
        
        # Extract key information from both files
        echo "📋 System Information Comparison:"
        echo "Local OS: $(grep "^OS:" "$LOCAL_DIAGNOSTIC" | cut -d' ' -f2-)"
        echo "Act OS:   $(grep "^OS:" "$ACT_DIAGNOSTIC" | cut -d' ' -f2- || echo 'Not found')"
        echo ""
        
        echo "📋 User Information Comparison:"
        echo "Local User: $(grep "^User:" "$LOCAL_DIAGNOSTIC" | cut -d' ' -f2-)"
        echo "Act User:   $(grep "^User:" "$ACT_DIAGNOSTIC" | cut -d' ' -f2- || echo 'Not found')"
        echo ""
        
        echo "📋 Flutter Version Comparison:"
        echo "Local Flutter:"
        grep -A 3 "Flutter version:" "$LOCAL_DIAGNOSTIC" | tail -3
        echo ""
        echo "Act Flutter:"
        grep -A 3 "Flutter version:" "$ACT_DIAGNOSTIC" | tail -3 || echo 'Not found'
        echo ""
        
        echo "📋 Git Information Comparison:"
        echo "Local Git:"
        grep "Last commit:" "$LOCAL_DIAGNOSTIC"
        grep "SOURCE_DATE_EPOCH calculation:" "$LOCAL_DIAGNOSTIC"
        echo ""
        echo "Act Git:"
        grep "Last commit:" "$ACT_DIAGNOSTIC" || echo 'Not found'
        grep "SOURCE_DATE_EPOCH calculation:" "$ACT_DIAGNOSTIC" || echo 'Not found'
        echo ""
        
        echo "📋 Environment Variables Comparison:"
        echo "Local HOME: $(grep "^HOME:" "$LOCAL_DIAGNOSTIC" | cut -d' ' -f2-)"
        echo "Act HOME:   $(grep "^HOME:" "$ACT_DIAGNOSTIC" | cut -d' ' -f2- || echo 'Not found')"
        echo ""
        echo "Local PWD: $(grep "^PWD:" "$LOCAL_DIAGNOSTIC" | cut -d' ' -f2-)"
        echo "Act PWD:   $(grep "^PWD:" "$ACT_DIAGNOSTIC" | cut -d' ' -f2- || echo 'Not found')"
        echo ""
        
        echo "📋 Cache Information Comparison:"
        echo "Local Global Cache:"
        grep -A 2 "Global pub cache" "$LOCAL_DIAGNOSTIC" | tail -2
        echo ""
        echo "Act Global Cache:"
        grep -A 2 "Global pub cache" "$ACT_DIAGNOSTIC" | tail -2 || echo 'Not found'
        echo ""
        
        # Generate summary
        print_status $YELLOW "📊 Analysis Summary:"
        echo "  - Local diagnostic: $LOCAL_DIAGNOSTIC"
        echo "  - Act diagnostic: $ACT_DIAGNOSTIC"
        echo "  - Use 'diff $LOCAL_DIAGNOSTIC $ACT_DIAGNOSTIC' for detailed comparison"
        
    else
        print_status $RED "❌ Act environment diagnostic not found"
        print_status $YELLOW "Make sure the act workflow completed and generated the diagnostic file"
    fi
    
else
    print_status $RED "❌ Act tool not found"
    print_status $YELLOW "Install act tool to compare environments: https://github.com/nektos/act"
fi

print_status $BLUE "🔗 Next steps:"
echo "  1. Review the environment comparison above"
echo "  2. Check for differences in Flutter versions, paths, or environment variables"
echo "  3. Look for cache or dependency resolution differences"
echo "  4. Use 'diff' command to compare diagnostic files in detail"
