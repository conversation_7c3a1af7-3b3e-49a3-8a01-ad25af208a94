#!/bin/bash

# WHPH - F-Droid Build Simulation Script
# This script simulates the exact F-Droid build process to verify hash consistency
# Usage: ./test_fdroid_build.sh

set -e

echo "🔍 F-Droid Build Simulation"
echo "=========================="
echo "This script simulates the exact F-Droid build process"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Clean any existing build artifacts (skip permission errors)
print_status $YELLOW "🧹 Cleaning build environment (ignoring permission errors)..."
rm -rf build/ .pub-cache/ 2>/dev/null || true

# Set up F-Droid environment variables (exactly as in F-Droid metadata)
print_status $BLUE "⚙️  Setting up F-Droid environment..."

export PUB_CACHE=$(pwd)/.pub-cache
export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
export FLUTTER_STORAGE_BASE_URL=https://storage.googleapis.com
export TZ=UTC
export LC_ALL=C
export LANG=C

print_status $GREEN "✅ F-Droid environment variables set:"
echo "  PUB_CACHE: $PUB_CACHE"
echo "  SOURCE_DATE_EPOCH: $SOURCE_DATE_EPOCH"
echo "  FLUTTER_STORAGE_BASE_URL: $FLUTTER_STORAGE_BASE_URL"
echo "  TZ: $TZ"
echo "  LC_ALL: $LC_ALL"
echo "  LANG: $LANG"
echo ""

# F-Droid prebuild steps (exactly as in metadata)
print_status $BLUE "📋 F-Droid prebuild steps..."
flutter config --no-analytics
flutter clean
flutter packages pub get

print_status $GREEN "✅ Prebuild completed"
echo ""

# F-Droid build steps (exactly as in metadata)
print_status $BLUE "🔨 F-Droid build steps..."
flutter clean
flutter packages pub get

# Build APK with exact F-Droid parameters
flutter build apk --release \
    --build-name=0.9.10 \
    --build-number=47 \
    --split-debug-info=build/app/outputs/symbols \
    --tree-shake-icons \
    --dart-define=FLUTTER_WEB_USE_SKIA=true \
    --target-platform=android-arm64,android-arm,android-x64

# Verify build output
APK_PATH="build/app/outputs/flutter-apk/app-release.apk"
if [ -f "$APK_PATH" ]; then
    APK_SIZE=$(stat -c%s "$APK_PATH")
    APK_HASH=$(sha256sum "$APK_PATH" | cut -d' ' -f1)
    
    print_status $GREEN "✅ F-Droid simulation completed successfully!"
    echo ""
    echo "📦 F-Droid Build Output:"
    echo "  Path: $APK_PATH"
    echo "  Size: $APK_SIZE bytes ($(awk "BEGIN {printf \"%.2f\", $APK_SIZE / 1024 / 1024}") MB)"
    echo "  SHA256: $APK_HASH"
    echo ""
    
    # Compare with expected hash
    EXPECTED_HASH="ec15919ec019aec726f46976b4818e932e241c4d1ba510bdff3d889129a9e448"
    EXPECTED_SIZE="133707069"
    
    print_status $BLUE "🔍 Comparing with expected results:"
    echo "  Expected hash: $EXPECTED_HASH"
    echo "  Actual hash:   $APK_HASH"
    echo "  Expected size: $EXPECTED_SIZE bytes"
    echo "  Actual size:   $APK_SIZE bytes"
    echo ""
    
    if [ "$APK_HASH" = "$EXPECTED_HASH" ]; then
        print_status $GREEN "🎉 SUCCESS: F-Droid simulation produces identical hash!"
        print_status $GREEN "✅ F-Droid signature verification will pass"
        
        if [ "$APK_SIZE" = "$EXPECTED_SIZE" ]; then
            print_status $GREEN "✅ File size also matches perfectly"
        else
            SIZE_DIFF=$((APK_SIZE - EXPECTED_SIZE))
            print_status $YELLOW "⚠️  File size differs by $SIZE_DIFF bytes"
        fi
        
    else
        print_status $RED "❌ FAILURE: F-Droid simulation produces different hash!"
        print_status $RED "⚠️  F-Droid signature verification will fail"
        
        echo ""
        print_status $BLUE "🔧 Possible causes:"
        echo "  1. Missing security validation step"
        echo "  2. Different Flutter command execution"
        echo "  3. Environment variable differences"
        echo "  4. Cache state differences"
        
        # Save the APK for analysis
        cp "$APK_PATH" "fdroid_simulation.apk"
        print_status $YELLOW "📁 APK saved as fdroid_simulation.apk for analysis"
    fi
    
else
    print_status $RED "❌ F-Droid simulation failed - APK not found at $APK_PATH"
    exit 1
fi

print_status $BLUE "📋 F-Droid Simulation Summary:"
echo "  - Environment: Simulated F-Droid build environment"
echo "  - Commands: Exact match with F-Droid metadata"
echo "  - Parameters: Identical to F-Droid configuration"
echo "  - Result: $([ "$APK_HASH" = "$EXPECTED_HASH" ] && echo "PASS" || echo "FAIL")"
